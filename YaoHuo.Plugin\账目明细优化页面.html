<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>账目明细</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
    <style>
        :where([class^="ri-"])::before { content: "\f3c2"; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        .filter-section {
            transition: max-height 0.3s ease-in-out;
            overflow: hidden;
        }
        .table-container {
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }
        .table-row:nth-child(even) {
            background-color: rgba(243, 244, 246, 0.5);
        }
        .table-row:nth-child(odd) {
            background-color: white;
        }
        input[type="number"]::-webkit-inner-spin-button,
        input[type="number"]::-webkit-outer-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }
        input[type="number"] {
            -moz-appearance: textfield;
        }
    </style>
    <script>tailwind.config={theme:{extend:{colors:{primary:'#4f46e5',secondary:'#10b981'},borderRadius:{'none':'0px','sm':'4px',DEFAULT:'8px','md':'12px','lg':'16px','xl':'20px','2xl':'24px','3xl':'32px','full':'9999px','button':'8px'}}}}</script>
</head>
<body class="bg-gray-50 text-gray-800">
    <!-- 顶部导航栏 -->
    <div class="fixed top-0 left-0 w-full bg-white shadow-sm z-10">
        <div class="flex items-center justify-center h-12 px-4 border-b border-gray-100">
            <h1 class="text-lg font-semibold">账目明细</h1>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="pt-14 pb-16 px-4">
        <!-- 查询区域 -->
        <div class="bg-white rounded-lg shadow-sm mb-4 overflow-hidden">
            <div class="p-3 border-b border-gray-100 flex items-center justify-between">
                <span class="text-sm font-medium">筛选条件</span>
                <button id="toggleFilter" class="w-6 h-6 flex items-center justify-center text-gray-500">
                    <i class="ri-arrow-down-s-line ri-lg"></i>
                </button>
            </div>
            
            <div id="filterSection" class="filter-section px-3 py-3 space-y-3">
                <div class="grid grid-cols-2 gap-3">
                    <div>
                        <label class="block text-xs text-gray-500 mb-1">年份</label>
                        <input type="number" value="2025" class="w-full h-10 px-3 rounded border border-gray-200 focus:border-primary focus:ring-1 focus:ring-primary focus:outline-none text-sm">
                    </div>
                    <div>
                        <label class="block text-xs text-gray-500 mb-1">月份</label>
                        <input type="number" value="6" min="1" max="12" class="w-full h-10 px-3 rounded border border-gray-200 focus:border-primary focus:ring-1 focus:ring-primary focus:outline-none text-sm">
                    </div>
                </div>
                
                <div>
                    <label class="block text-xs text-gray-500 mb-1">ID 号</label>
                    <input type="number" placeholder="请输入 ID 号" value="1000" class="w-full h-10 px-3 rounded border border-gray-200 focus:border-primary focus:ring-1 focus:ring-primary focus:outline-none text-sm">
                </div>
                
                <div>
                    <label class="block text-xs text-gray-500 mb-1">项目名称</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                            <i class="ri-search-line text-gray-400"></i>
                        </div>
                        <input type="text" placeholder="关键字" value="关键字" class="w-full h-10 pl-9 pr-3 rounded border border-gray-200 focus:border-primary focus:ring-1 focus:ring-primary focus:outline-none text-sm">
                    </div>
                </div>
                
                <div class="flex justify-end">
                    <button class="h-9 px-4 bg-primary text-white rounded-button text-sm font-medium cursor-pointer flex items-center">
                        <i class="ri-search-line mr-1"></i>
                        查询
                    </button>
                </div>
            </div>
        </div>

        <!-- 表格区域 -->
        <div class="bg-white rounded-lg shadow-sm overflow-hidden">
            <!-- 表头 -->
            <div class="grid grid-cols-5 text-xs font-medium text-gray-500 border-b border-gray-100">
                <div class="p-3">项目</div>
                <div class="p-3">交易数量</div>
                <div class="p-3">账户余额</div>
                <div class="p-3">操作者</div>
                <div class="p-3">时间</div>
            </div>
            
            <!-- 表格内容 -->
            <div class="overflow-y-auto" style="max-height: 400px;">
                <!-- 表格行 -->
                <div class="table-row grid grid-cols-5 text-sm border-b border-gray-100">
                    <div class="p-3 flex items-center">
                        <span class="w-1 h-5 bg-primary rounded-full mr-2"></span>
                        <span>论坛回帖</span>
                    </div>
                    <div class="p-3 font-medium text-green-500">+30 积分</div>
                    <div class="p-3">1033407</div>
                    <div class="p-3 text-gray-600">Clover<br><span class="text-xs text-gray-400">(ID:1000)</span></div>
                    <div class="p-3 text-gray-600">2025/6/6<br><span class="text-xs">07:42:00</span></div>
                </div>
                
                <div class="table-row grid grid-cols-5 text-sm border-b border-gray-100">
                    <div class="p-3 flex items-center">
                        <span class="w-1 h-5 bg-primary rounded-full mr-2"></span>
                        <span>论坛回帖</span>
                    </div>
                    <div class="p-3 font-medium text-green-500">+30 积分</div>
                    <div class="p-3">1033377</div>
                    <div class="p-3 text-gray-600">Clover<br><span class="text-xs text-gray-400">(ID:1000)</span></div>
                    <div class="p-3 text-gray-600">2025/6/6<br><span class="text-xs">03:34:30</span></div>
                </div>
                
                <div class="table-row grid grid-cols-5 text-sm border-b border-gray-100">
                    <div class="p-3 flex items-center">
                        <span class="w-1 h-5 bg-primary rounded-full mr-2"></span>
                        <span>论坛回帖</span>
                    </div>
                    <div class="p-3 font-medium text-green-500">+30 积分</div>
                    <div class="p-3">1033347</div>
                    <div class="p-3 text-gray-600">Clover<br><span class="text-xs text-gray-400">(ID:1000)</span></div>
                    <div class="p-3 text-gray-600">2025/6/6<br><span class="text-xs">09:09:05</span></div>
                </div>
                
                <div class="table-row grid grid-cols-5 text-sm border-b border-gray-100">
                    <div class="p-3 flex items-center">
                        <span class="w-1 h-5 bg-primary rounded-full mr-2"></span>
                        <span>论坛回帖</span>
                    </div>
                    <div class="p-3 font-medium text-green-500">+30 积分</div>
                    <div class="p-3">1033317</div>
                    <div class="p-3 text-gray-600">Clover<br><span class="text-xs text-gray-400">(ID:1000)</span></div>
                    <div class="p-3 text-gray-600">2025/6/6<br><span class="text-xs">08:08:27</span></div>
                </div>
                
                <div class="table-row grid grid-cols-5 text-sm border-b border-gray-100">
                    <div class="p-3 flex items-center">
                        <span class="w-1 h-5 bg-primary rounded-full mr-2"></span>
                        <span>论坛回帖</span>
                    </div>
                    <div class="p-3 font-medium text-green-500">+30 积分</div>
                    <div class="p-3">1033287</div>
                    <div class="p-3 text-gray-600">Clover<br><span class="text-xs text-gray-400">(ID:1000)</span></div>
                    <div class="p-3 text-gray-600">2025/6/6<br><span class="text-xs">06:06:28</span></div>
                </div>
                
                <div class="table-row grid grid-cols-5 text-sm border-b border-gray-100">
                    <div class="p-3 flex items-center">
                        <span class="w-1 h-5 bg-yellow-500 rounded-full mr-2"></span>
                        <span>论坛回帖</span>
                    </div>
                    <div class="p-3 font-medium text-green-500">+230 积分</div>
                    <div class="p-3">1033257</div>
                    <div class="p-3 text-gray-600">Clover<br><span class="text-xs text-gray-400">(ID:1000)</span></div>
                    <div class="p-3 text-gray-600">2025/6/6<br><span class="text-xs">05:05:22</span></div>
                </div>
                
                <div class="table-row grid grid-cols-5 text-sm border-b border-gray-100">
                    <div class="p-3 flex items-center">
                        <span class="w-1 h-5 bg-primary rounded-full mr-2"></span>
                        <span>论坛回帖</span>
                    </div>
                    <div class="p-3 font-medium text-green-500">+30 积分</div>
                    <div class="p-3">1033027</div>
                    <div class="p-3 text-gray-600">Clover<br><span class="text-xs text-gray-400">(ID:1000)</span></div>
                    <div class="p-3 text-gray-600">2025/6/5<br><span class="text-xs">23:47:28</span></div>
                </div>
                
                <div class="table-row grid grid-cols-5 text-sm border-b border-gray-100">
                    <div class="p-3 flex items-center">
                        <span class="w-1 h-5 bg-primary rounded-full mr-2"></span>
                        <span>论坛回帖</span>
                    </div>
                    <div class="p-3 font-medium text-green-500">+30 积分</div>
                    <div class="p-3">1032997</div>
                    <div class="p-3 text-gray-600">Clover<br><span class="text-xs text-gray-400">(ID:1000)</span></div>
                    <div class="p-3 text-gray-600">2025/6/5<br><span class="text-xs">23:44:12</span></div>
                </div>
            </div>
            
            <!-- 分页控制 -->
            <div class="p-3 border-t border-gray-100 flex items-center justify-between text-sm">
                <div class="text-gray-500">第 1/13 页，共 185 条</div>
                <div class="flex">
                    <button class="h-9 px-4 border border-gray-200 rounded-l-button text-gray-600 cursor-pointer">上一页</button>
                    <button class="h-9 px-4 border border-gray-200 border-l-0 rounded-r-button text-gray-600 cursor-pointer">下一页</button>
                </div>
            </div>
        </div>
        
        <!-- 底部提示 -->
        <div class="mt-4 text-center text-xs text-gray-400">
            仅展示本月会员记录（保留本月）
        </div>
    </div>

    <!-- 底部标签栏 -->
    <div class="fixed bottom-0 left-0 w-full bg-white border-t border-gray-200 flex items-center justify-around h-14 z-10">
        <a href="#" class="flex flex-col items-center justify-center w-1/5">
            <div class="w-6 h-6 flex items-center justify-center">
                <i class="ri-home-line text-gray-400"></i>
            </div>
            <span class="text-xs mt-0.5 text-gray-400">首页</span>
        </a>
        <a href="#" class="flex flex-col items-center justify-center w-1/5">
            <div class="w-6 h-6 flex items-center justify-center">
                <i class="ri-file-list-line text-primary"></i>
            </div>
            <span class="text-xs mt-0.5 text-primary">明细</span>
        </a>
        <a href="#" class="flex flex-col items-center justify-center w-1/5">
            <div class="w-6 h-6 flex items-center justify-center">
                <i class="ri-exchange-line text-gray-400"></i>
            </div>
            <span class="text-xs mt-0.5 text-gray-400">交易</span>
        </a>
        <a href="#" class="flex flex-col items-center justify-center w-1/5">
            <div class="w-6 h-6 flex items-center justify-center">
                <i class="ri-user-line text-gray-400"></i>
            </div>
            <span class="text-xs mt-0.5 text-gray-400">我的</span>
        </a>
    </div>

    <!-- JavaScript 交互 -->
    <script id="filterToggle">
        document.addEventListener('DOMContentLoaded', function() {
            const toggleBtn = document.getElementById('toggleFilter');
            const filterSection = document.getElementById('filterSection');
            let isExpanded = true;
            
            toggleBtn.addEventListener('click', function() {
                isExpanded = !isExpanded;
                if (isExpanded) {
                    filterSection.style.maxHeight = '500px';
                    toggleBtn.innerHTML = '<i class="ri-arrow-down-s-line ri-lg"></i>';
                } else {
                    filterSection.style.maxHeight = '0';
                    toggleBtn.innerHTML = '<i class="ri-arrow-right-s-line ri-lg"></i>';
                }
            });
        });
    </script>
    
    <script id="tableInteractions">
        document.addEventListener('DOMContentLoaded', function() {
            // 表格行点击事件
            const tableRows = document.querySelectorAll('.table-row');
            tableRows.forEach(row => {
                row.addEventListener('click', function() {
                    // 这里可以添加点击行后的详情展示逻辑
                    const transactionId = this.querySelector('div:nth-child(3)').textContent.trim();
                    const transactionType = this.querySelector('div:nth-child(1) span:last-child').textContent.trim();
                    
                    // 创建一个模态框展示详情
                    showTransactionDetail(transactionId, transactionType);
                });
            });
            
            function showTransactionDetail(id, type) {
                // 创建模态框
                const modal = document.createElement('div');
                modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                
                const modalContent = document.createElement('div');
                modalContent.className = 'bg-white rounded-lg w-5/6 max-w-md';
                
                modalContent.innerHTML = `
                    <div class="p-4 border-b border-gray-100 flex items-center justify-between">
                        <h3 class="font-medium">交易详情</h3>
                        <button id="closeModal" class="w-6 h-6 flex items-center justify-center text-gray-500">
                            <i class="ri-close-line"></i>
                        </button>
                    </div>
                    <div class="p-4 space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-500">交易类型</span>
                            <span>${type}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-500">交易金额</span>
                            <span class="text-green-500">+10,000</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-500">账户余额</span>
                            <span>${(301637).toLocaleString()}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-500">交易时间</span>
                            <span>2025-06-06 01:21:00</span>
                        </div>
                        <div>
                            <div class="flex justify-between">
                                <span class="text-gray-500">操作人</span>
                                <span>音华羽</span>
                            </div>
                            <div class="flex justify-between mt-1">
                                <span class="text-gray-500">用户ID</span>
                                <span>11633</span>
                            </div>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-500">备注</span>
                            <span>花了￥1购买</span>
                        </div>
                    </div>
                    <div class="p-4 border-t border-gray-100 flex justify-end">
                        <button id="confirmModal" class="h-9 px-4 bg-primary text-white rounded-button text-sm cursor-pointer">确定</button>
                    </div>
                `;
                
                modal.appendChild(modalContent);
                document.body.appendChild(modal);
                
                // 关闭模态框
                document.getElementById('closeModal').addEventListener('click', function() {
                    document.body.removeChild(modal);
                });
                
                document.getElementById('confirmModal').addEventListener('click', function() {
                    document.body.removeChild(modal);
                });
            }
        });
    </script>
</body>
</html>