{{#if Message.HasMessage}}
<div class="message {{Message.Type}}">
    <div>
        {{Message.Content}}
    </div>
</div>
{{/if}}

<!-- 统计面板（占位符） -->
<div class="card hidden" id="stats-panel">
    <div class="bg-gradient-to-br from-primary to-primary-dark p-4">
        <div class="flex items-center justify-between mb-2">
            <div class="text-white">
                <h3 class="text-lg font-medium">月度统计</h3>
                <p class="text-sm opacity-80">当前：{{Filter.ToYear}}年{{Filter.ToMonth}}月</p>
            </div>
        </div>
    </div>
    
    <div class="stats-grid" id="statsPanel">
        <div class="stats-item">
            <span class="text-text-primary font-medium transition-colors">+{{Statistics.FormattedMonthlyIncome}}</span>
            <span class="text-xs text-text-secondary transition-colors">本月收入</span>
        </div>
        <div class="stats-item">
            <span class="text-text-primary font-medium transition-colors">-{{Statistics.FormattedMonthlyExpense}}</span>
            <span class="text-xs text-text-secondary transition-colors">本月支出</span>
        </div>
        <div class="stats-item">
            <span class="text-text-primary font-medium transition-colors">{{Statistics.FormattedMonthlyNet}}</span>
            <span class="text-xs text-text-secondary transition-colors">本月净收入</span>
        </div>
        <div class="stats-item">
            <span class="text-text-primary font-medium transition-colors">功能开发中</span>
            <span class="text-xs text-text-secondary transition-colors">敬请期待</span>
        </div>
    </div>
</div>

<!-- 筛选卡片 -->
<div class="card">
    <div class="card-header">
        <div class="card-title">
            <i data-lucide="filter" class="card-icon"></i>
            筛选条件
        </div>
    </div>
    <div class="card-body">
        <form action="{{SiteInfo.HttpStart}}bbs/banklist.aspx" method="get" id="filter-form">
            <input type="hidden" name="action" value="search">
            <input type="hidden" name="siteid" value="{{SiteInfo.SiteId}}">
            <input type="hidden" name="classid" value="{{SiteInfo.ClassId}}">
            <input type="hidden" name="backurl" value="{{SiteInfo.BackUrl}}">
            
            <!-- 年份和月份 -->
            <div class="grid-2">
                <div class="form-group">
                    <label class="form-label">年份</label>
                    <select class="form-select" name="toyear" id="toyear">
                        {{#each Filter.YearOptions}}
                        <option value="{{Value}}" {{#if Selected}}selected{{/if}}>{{Text}}</option>
                        {{/each}}
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">月份</label>
                    <select class="form-select" name="tomonth" id="tomonth">
                        {{#each Filter.MonthOptions}}
                        <option value="{{Value}}" {{#if Selected}}selected{{/if}}>{{Text}}</option>
                        {{/each}}
                    </select>
                </div>
            </div>
            
            <!-- 管理员专用：ID号输入 -->
            {{#if IsAdmin}}
            <div class="form-group mb-4">
                <label class="form-label">用户ID</label>
                <input type="text" class="form-input" name="key" value="{{Filter.Key}}" placeholder="输入用户ID（留空查看所有用户）">
            </div>
            {{/if}}
            
            <!-- 搜索类型和关键字 -->
            <div class="grid-2">
                <div class="form-group">
                    <label class="form-label">搜索类型</label>
                    <select class="form-select" name="typeid" id="typeid">
                        {{#each Filter.SearchTypeOptions}}
                        <option value="{{Value}}" {{#if Selected}}selected{{/if}}>{{Text}}</option>
                        {{/each}}
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">关键字</label>
                    <input type="text" class="form-input" name="typekey" value="{{Filter.TypeKey}}" placeholder="输入搜索关键字">
                </div>
            </div>
            
            <!-- 按钮组 -->
            <div class="grid-2">
                <button type="submit" class="btn btn-primary">
                    <i data-lucide="search" class="w-4 h-4 mr-1"></i>
                    查询
                </button>
                <button type="button" class="btn btn-outline" onclick="resetFilter()">
                    <i data-lucide="rotate-ccw" class="w-4 h-4 mr-1"></i>
                    重置
                </button>
            </div>
        </form>
    </div>
</div>

<!-- 交易记录表格 -->
<div class="card">
    <div class="card-header">
        <div class="card-title">
            <i data-lucide="receipt" class="card-icon"></i>
            交易记录
            {{#if Pagination.TotalItems}}
            <span class="text-sm text-text-secondary font-normal ml-1">(共{{Pagination.TotalItems}}条)</span>
            {{/if}}
        </div>
    </div>
    <div class="card-body p-0">
        {{#if BankLogList}}
        <div class="table-responsive">
            <table class="w-full">
                <thead class="bg-bg-gray-50 border-b border-border-light">
                    <tr>
                        <th class="px-4 py-3 text-center text-xs font-medium text-text-secondary uppercase tracking-wider">项目</th>
                        <th class="px-4 py-3 text-center text-xs font-medium text-text-secondary uppercase tracking-wider">交易金额</th>
                        <th class="px-4 py-3 text-center text-xs font-medium text-text-secondary uppercase tracking-wider">账户余额</th>
                        <th class="px-4 py-3 text-center text-xs font-medium text-text-secondary uppercase tracking-wider">操作人</th>
                        <th class="px-4 py-3 text-center text-xs font-medium text-text-secondary uppercase tracking-wider">时间</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-border-light">
                    {{#each BankLogList}}
                    <tr class="hover:bg-bg-gray-50 transition-colors">
                        <td class="px-4 py-3 text-center">
                            <div class="text-sm text-text-primary break-words">{{ActionName}}</div>
                            {{#if Remark}}
                            <div class="text-xs text-text-secondary mt-1 break-words">{{Remark}}</div>
                            {{/if}}
                        </td>
                        <td class="px-4 py-3 text-center">
                            {{#if IsPositiveAmount}}
                            <span class="text-success break-words">+{{FormattedMoney}}</span>
                            {{else}}
                            <span class="text-danger break-words">{{FormattedMoney}}</span>
                            {{/if}}
                        </td>
                        <td class="px-4 py-3 text-center">
                            <span class="text-text-secondary break-words">{{LeftMoney}}</span>
                        </td>
                        <td class="px-4 py-3 text-center">
                            <div class="text-sm text-text-secondary break-words">{{OperaNickname}}</div>
                            <div class="text-xs text-text-secondary break-words">ID: {{OperaUserId}}</div>
                        </td>
                        <td class="px-4 py-3 text-center">
                            <span class="text-sm text-text-secondary break-words">{{FormattedAddTime}}</span>
                        </td>
                    </tr>
                    {{/each}}
                </tbody>
            </table>
        </div>
        {{else}}
        <div class="text-center py-8 text-text-secondary">
            <i data-lucide="inbox" class="w-12 h-12 mx-auto mb-3 opacity-50"></i>
            <p>暂无交易记录</p>
        </div>
        {{/if}}
    </div>
</div>

<!-- 分页导航 -->
{{#if Pagination.ShowPagination}}
<div class="card">
    <div class="card-body">
        <div class="flex justify-between items-center">
            <div class="text-sm text-text-secondary">
                第 {{Pagination.CurrentPage}} 页，共 {{Pagination.TotalPages}} 页，{{Pagination.TotalItems}} 条记录
            </div>
            <div class="flex items-center gap-2">
                {{#if Pagination.IsFirstPage}}
                <button class="btn btn-outline disabled" disabled>
                    <i data-lucide="chevron-left" class="w-4 h-4"></i>
                    上一页
                </button>
                {{else}}
                <button class="btn btn-outline" onclick="navigatePage({{Pagination.CurrentPage}} - 1)">
                    <i data-lucide="chevron-left" class="w-4 h-4"></i>
                    上一页
                </button>
                {{/if}}
                
                {{#if Pagination.IsLastPage}}
                <button class="btn btn-outline disabled" disabled>
                    下一页
                    <i data-lucide="chevron-right" class="w-4 h-4"></i>
                </button>
                {{else}}
                <button class="btn btn-outline" onclick="navigatePage({{Pagination.CurrentPage}} + 1)">
                    下一页
                    <i data-lucide="chevron-right" class="w-4 h-4"></i>
                </button>
                {{/if}}
            </div>
        </div>
    </div>
</div>
{{/if}}

<!-- 管理员操作 -->
{{#if IsAdmin}}
<div class="mx-4 mt-4">
    <button class="w-full bg-danger text-white border border-danger rounded-md px-4 py-3 font-medium text-sm transition-all duration-200 hover:bg-danger-dark hover:border-danger-dark flex items-center justify-center" id="clear-records-btn">
        <i data-lucide="trash-2" class="w-4 h-4 mr-2"></i>
        删除所有会员记录(保留本月)
    </button>
</div>
{{/if}}



<script>
    // 初始化Lucide图标
    document.addEventListener('DOMContentLoaded', function() {
        lucide.createIcons();
        initializeFilter();
        
        // 绑定清空记录按钮事件
        const clearRecordsBtn = document.getElementById('clear-records-btn');
        if (clearRecordsBtn) {
            clearRecordsBtn.addEventListener('click', clearAllRecords);
        }
    });

    // 初始化筛选表单
    function initializeFilter() {
        // 初始化年份选项
        const currentYear = new Date().getFullYear();
        const yearSelect = document.getElementById('toyear');
        if (yearSelect && yearSelect.options.length === 0) {
            for (let year = currentYear; year >= currentYear - 5; year--) {
                const option = new Option(year + '年', year);
                if (year.toString() === '{{Filter.ToYear}}') {
                    option.selected = true;
                }
                yearSelect.add(option);
            }
        }

        // 初始化月份选项
        const monthSelect = document.getElementById('tomonth');
        if (monthSelect && monthSelect.options.length === 0) {
            for (let month = 1; month <= 12; month++) {
                const option = new Option(month + '月', month);
                if (month.toString() === '{{Filter.ToMonth}}') {
                    option.selected = true;
                }
                monthSelect.add(option);
            }
        }

        // 设置搜索类型选中状态
        const typeIdSelect = document.getElementById('typeid');
        if (typeIdSelect) {
            typeIdSelect.value = '{{Filter.TypeId}}';
        }
    }

    // 重置筛选条件
    function resetFilter() {
        const form = document.getElementById('filter-form');
        const currentYear = new Date().getFullYear();
        const currentMonth = new Date().getMonth() + 1;
        
        // 重置到当前年月
        document.getElementById('toyear').value = currentYear;
        document.getElementById('tomonth').value = currentMonth;
        document.getElementById('typeid').value = '';
        
        // 清空输入框
        const inputs = form.querySelectorAll('input[type="text"]');
        inputs.forEach(input => input.value = '');
        
        // 提交表单
        form.submit();
    }

    // 分页导航
    function navigatePage(page) {
        const form = document.getElementById('filter-form');
        
        // 添加页码参数
        let pageInput = form.querySelector('input[name="page"]');
        if (!pageInput) {
            pageInput = document.createElement('input');
            pageInput.type = 'hidden';
            pageInput.name = 'page';
            form.appendChild(pageInput);
        }
        pageInput.value = page;
        
        // 提交表单
        form.submit();
    }

    // 统计面板切换
    function toggleStats() {
        const statsPanel = document.getElementById('stats-panel');
        statsPanel.classList.toggle('hidden');
    }



    // 返回上一页
    function goBack() {
        history.back();
    }

    // 自定义确认对话框函数
    function showCustomConfirm(message, onConfirm) {
        const confirmDialogOverlay = document.createElement('div');
        confirmDialogOverlay.style.cssText = `
            position: fixed; inset: 0; background-color: rgba(0,0,0,0.5);
            display: flex; align-items: center; justify-content: center;
            z-index: 1030;
        `;

        const confirmDialogContent = document.createElement('div');
        confirmDialogContent.style.cssText = `
            background-color: white;
            border-radius: 12px;
            padding: 24px 20px 20px;
            width: 85%; max-width: 360px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
            text-align: center;
        `;

        confirmDialogContent.innerHTML = `
            <h3 style="font-size: 18px; font-weight: 600; color: #1f2937; margin-bottom: 12px;">确认操作</h3>
            <p style="color: #6b7280; margin-bottom: 24px; font-size: 15px; line-height: 1.5;">${message}</p>
            <div style="display: flex; justify-content: center; gap: 12px;">
                <button class="custom-confirm-btn custom-confirm-delete" id="confirmCustomConfirm">确定</button>
                <button class="custom-confirm-btn custom-confirm-cancel" id="cancelCustomConfirm">取消</button>
            </div>
        `;

        confirmDialogOverlay.appendChild(confirmDialogContent);
        document.body.appendChild(confirmDialogOverlay);

        // 关闭对话框函数
        function closeDialog() {
            if (document.body.contains(confirmDialogOverlay)) {
                document.body.removeChild(confirmDialogOverlay);
            }
        }

        const cancelBtn = document.getElementById('cancelCustomConfirm');
        if(cancelBtn) cancelBtn.onclick = closeDialog;

        const confirmBtn = document.getElementById('confirmCustomConfirm');
        if(confirmBtn) confirmBtn.onclick = () => {
            onConfirm();
            closeDialog();
        };

        // 点击遮罩关闭 - 只有点击遮罩本身才关闭，点击内容区域不关闭
        confirmDialogOverlay.addEventListener('click', function(e) {
            if (e.target === confirmDialogOverlay) {
                closeDialog();
            }
        });
    }

    // 删除所有会员记录（管理员功能）
    function clearAllRecords() {
        showCustomConfirm('确定要删除所有会员的历史交易记录吗？<br/>此操作将保留本月记录，删除本月之前的所有记录。<br/>此操作不可恢复！', function() {
            const form = document.createElement('form');
            form.method = 'GET';
            form.action = '{{SiteInfo.HttpStart}}bbs/banklist.aspx';
            
            // 添加必要参数
            const params = {
                action: 'gomod',
                siteid: '{{SiteInfo.SiteId}}',
                classid: '{{SiteInfo.ClassId}}',
                key: '{{Filter.Key}}',
                toyear: '{{Filter.ToYear}}',
                tomonth: '{{Filter.ToMonth}}',
                backurl: '{{SiteInfo.BackUrl}}'
            };
            
            for (const key in params) {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = key;
                input.value = params[key];
                form.appendChild(input);
            }
            
            document.body.appendChild(form);
            form.submit();
        });
    }
</script> 