<!-- 个人信息卡片 -->
<div class="card">
    <div class="bg-gradient-to-br from-primary to-primary-dark p-4">
        <div class="flex items-center justify-between">
            <div class="text-white">
                <div class="flex items-center mb-1 text-left">
                    <span class="text-2xl font-bold">{{UserInfo.DisplayName}}</span>
                </div>
                <div class="mt-1 text-left">
                    <span class="ml-0 bg-white bg-opacity-20 text-white text-xs py-0.5 px-2 rounded-full opacity-80">ID: {{UserInfo.UserId}}</span>
                </div>
            </div>
            <!-- 编辑资料下拉菜单 -->
            <div class="edit-profile dropdown" id="profile-edit-dropdown">
                <i data-lucide="pen-line" class="w-4 h-4 text-white"></i>
                <span class="text-white text-sm font-medium ml-1">编辑资料</span>
                <i data-lucide="chevron-down" class="w-4 h-4 text-white"></i>
                <div class="dropdown-menu" id="profile-edit-dropdown-menu">
                    <div class="dropdown-item" onclick="window.location.href='{{Links.EditProfileUrl}}'">
                        <i data-lucide="user-cog" class="w-4 h-4 mr-2"></i>
                        <span>修改资料</span>
                    </div>
                    <div class="dropdown-item" onclick="window.location.href='{{Links.ChangePasswordUrl}}'">
                        <i data-lucide="key" class="w-4 h-4 mr-2"></i>
                        <span>更改密码</span>
                    </div>
                    <div class="dropdown-item" onclick="window.location.href='{{Links.ChangeAvatarUrl}}'">
                        <i data-lucide="user-circle" class="w-4 h-4 mr-2"></i>
                        <span>更换头像</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 数据统计 -->
    <div class="stats-grid">
        <div class="stats-item" onclick="window.location.href='{{Links.MailboxUrl}}'">
            <span class="text-text-primary font-medium transition-colors">{{Statistics.MessageDisplay}}</span>
            <span class="text-xs text-text-secondary transition-colors">信箱</span>
        </div>
        <div class="stats-item" onclick="window.location.href='{{Links.FriendsUrl}}'">
            <span class="text-text-primary font-medium transition-colors">{{Statistics.FriendCount}}</span>
            <span class="text-xs text-text-secondary transition-colors">好友</span>
        </div>
        <div class="stats-item" onclick="window.location.href='{{Links.PostsUrl}}'">
            <span class="text-text-primary font-medium transition-colors">{{Statistics.PostCount}}</span>
            <span class="text-xs text-text-secondary transition-colors">帖子</span>
        </div>
        <div class="stats-item" onclick="window.location.href='{{Links.RepliesUrl}}'">
            <span class="text-text-primary font-medium transition-colors">{{Statistics.ReplyCount}}</span>
            <span class="text-xs text-text-secondary transition-colors">回复</span>
        </div>
    </div>

    <!-- 等级进度 -->
    <div class="p-4">
        <div class="flex justify-between items-center mb-2">
            <span class="text-sm text-text-secondary">经验值: {{formatNumber UserInfo.Experience}}</span>
            <span class="text-sm text-primary">{{UserInfo.Level}}</span>
        </div>
        <div class="w-full h-2 bg-gray-200 rounded-sm overflow-hidden">
            <div class="h-full bg-gradient-to-r from-primary to-primary-light rounded-sm w-0 transition-all duration-extra-slow"></div>
        </div>
    </div>
</div>

<!-- 我的资产 -->
<div class="card">
    <div class="card-header">
        <h2 class="card-title">
            <i data-lucide="wallet" class="card-icon"></i>
            我的资产
        </h2>
    </div>
    <div class="card-body">
        <div class="flex justify-between items-center py-2 border-b border-border-light">
            <div class="text-sm text-text-secondary flex items-center">
                <i data-lucide="gem" class="w-4 h-4 mr-1"></i>
                我的{{Assets.MoneyName}}
            </div>
            <div class="font-medium text-primary font-semibold text-right">{{Assets.MoneyDisplay}}</div>
        </div>
        {{#if Assets.HasBankMoney}}
        <div class="flex justify-between items-center py-2 border-b border-border-light">
            <div class="text-sm text-text-secondary flex items-center">
                <i data-lucide="landmark" class="w-4 h-4 mr-1"></i>
                银行账户
            </div>
            <div class="font-medium text-warning font-semibold text-right">{{Assets.BankDisplay}}</div>
        </div>
        {{/if}}
        <div class="flex justify-between items-center py-2">
            <div class="text-sm text-text-secondary flex items-center">
                <i data-lucide="credit-card" class="w-4 h-4 mr-1"></i>
                我的RMB
            </div>
            <div class="font-medium text-primary font-semibold text-right">￥{{Assets.RMBDisplay}}</div>
        </div>
        <div class="grid-2 mt-4">
            <button class="btn btn-primary" onclick="window.location.href='{{Links.RechargeUrl}}'">
                <i data-lucide="plus" class="w-4 h-4 mr-1"></i>
                充值
            </button>
            <button class="btn btn-outline" onclick="window.location.href='{{Links.AccountDetailUrl}}'">
                <i data-lucide="receipt" class="w-4 h-4 mr-1"></i>
                明细
            </button>
        </div>
    </div>
</div>

<!-- 个人信息 -->
<div class="card">
    <div class="card-header">
        <h2 class="card-title">
            <i data-lucide="user-circle" class="card-icon"></i>
            个人信息
        </h2>
    </div>
    <div class="card-body">
        <div class="flex justify-between items-center py-2 border-b border-border-light">
            <div class="text-sm text-text-secondary flex items-center">
                <i data-lucide="user" class="w-4 h-4 mr-1"></i>
                我的身份
            </div>
            <div class="font-medium text-text-primary text-right">
                {{#if UserInfo.IsVip}}
                <span class="inline-flex items-center py-1 px-2 rounded text-xs font-medium border border-transparent transition-all bg-bg-vip text-danger border-danger border-opacity-20">{{{UserInfo.IdentityHtml}}}</span>
                {{else}}
                <span class="">{{{UserInfo.IdentityHtml}}}</span>
                {{/if}}
            </div>
        </div>
        <div class="flex justify-between items-center py-2 border-b border-border-light">
            <div class="text-sm text-text-secondary flex items-center">
                <i data-lucide="calendar" class="w-4 h-4 mr-1"></i>
                有效期至
            </div>
            <div class="font-medium text-text-primary text-right">
                {{#if UserInfo.HasEndTime}}
                <span>{{UserInfo.EndTime}}</span>
                <button class="btn-ghost" onclick="window.location.href='/bbs/BuyGroup.html'">续费</button>
                {{else}}
                <span>无期限</span>
                <button class="btn-ghost" onclick="window.location.href='/bbs/BuyGroup.html'">开通VIP</button>
                {{/if}}
            </div>
        </div>
        {{#if Permissions.HasAdminPermission}}
        <div class="flex justify-between items-center py-2">
            <div class="text-sm text-text-secondary flex items-center">
                <i data-lucide="shield" class="w-4 h-4 mr-1"></i>
                管理权限
            </div>
            <div class="font-medium text-text-primary text-right">
                <span class="inline-flex items-center py-1 px-2 rounded text-xs font-medium border border-transparent transition-all bg-bg-admin text-warning border-warning border-opacity-20">{{Permissions.AdminDisplay}}</span>
            </div>
        </div>
        {{/if}}
    </div>
</div>

<!-- 我的勋章 -->
<div class="card">
    <div class="card-header">
        <h2 class="card-title">
            <i data-lucide="award" class="card-icon"></i>
            我的勋章
        </h2>
    </div>
    <div class="card-body">
        {{#if Medals.HasMedals}}
        <div class="flex-wrap mb-4">{{{Medals.MedalHtml}}}</div>
        {{else}}
        <span class="text-text-secondary text-sm">暂无勋章</span>
        {{/if}}
        <div class="grid-2 mt-4">
            <button class="btn btn-outline flex items-center" onclick="window.location.href='{{Links.ApplyMedalUrl}}'">
                <i data-lucide="medal" class="w-4 h-4 mr-1"></i>
                申请勋章
            </button>
            <button class="btn btn-outline flex items-center" onclick="window.location.href='{{Links.BuyMedalUrl}}'">
                <i data-lucide="shopping-bag" class="w-4 h-4 mr-1"></i>
                购买勋章
            </button>
        </div>
    </div>
</div>

<!-- 我的内容 -->
<div class="card">
    <div class="card-header">
        <h2 class="card-title">
            <i data-lucide="settings" class="card-icon"></i>
            我的内容
        </h2>
    </div>
    <div class="card-body">
        <div class="grid-2">
            <button class="btn btn-outline flex items-center" onclick="window.location.href='{{Links.FavoritesUrl}}'">
                <i data-lucide="bookmark" class="w-4 h-4 mr-1"></i>
                我的收藏
            </button>
            <button class="btn btn-outline flex items-center" onclick="window.location.href='{{Links.AlbumUrl}}'">
                <i data-lucide="images" class="w-4 h-4 mr-1"></i>
                我的相册
            </button>
            <button class="btn btn-outline flex items-center" onclick="window.location.href='{{Links.ClanUrl}}'">
                <i data-lucide="users" class="w-4 h-4 mr-1"></i>
                我的家族
            </button>
            <button class="btn btn-outline flex items-center" onclick="window.location.href='{{Links.BlacklistUrl}}'">
                <i data-lucide="user-x" class="w-4 h-4 mr-1"></i>
                黑名单
            </button>
        </div>
    </div>
</div>

<!-- 网站规则 -->
<div class="card">
    <div class="card-header">
        <h2 class="card-title">
            <i data-lucide="book-open" class="card-icon"></i>
            网站规则
        </h2>
    </div>
    <div class="card-body">
        <div class="grid-3">
            <button class="btn btn-outline flex flex-col items-center justify-center h-auto p-2" onclick="window.location.href='{{SiteInfo.HttpStart}}bbs/tomoneyinfo.aspx?siteid={{SiteInfo.SiteId}}'">
                <i data-lucide="gem" class="w-4 h-4 mb-1"></i>
                <span class="text-xs">{{Assets.MoneyName}}规则</span>
            </button>
            <button class="btn btn-outline flex flex-col items-center justify-center h-auto p-2" onclick="window.location.href='{{SiteInfo.HttpStart}}bbs/tolvlinfo.aspx?siteid={{SiteInfo.SiteId}}'">
                <i data-lucide="trending-up" class="w-4 h-4 mb-1"></i>
                <span class="text-xs">等级规则</span>
            </button>
            <button class="btn btn-outline flex flex-col items-center justify-center h-auto p-2" onclick="window.location.href='{{SiteInfo.HttpStart}}bbs/totimeinfo.aspx?siteid={{SiteInfo.SiteId}}'">
                <i data-lucide="clock" class="w-4 h-4 mb-1"></i>
                <span class="text-xs">在线时间</span>
            </button>
        </div>
    </div>
</div>

{{#if Permissions.IsAdmin}}
<!-- 管理后台 -->
<div class="card">
    <div class="card-header">
        <h2 class="card-title">
            <i data-lucide="shield-check" class="card-icon"></i>
            管理后台
        </h2>
    </div>
    <div class="card-body">
        <div class="grid-2">
            <button class="btn btn-outline flex items-center" onclick="window.location.href='{{Links.AdminUrl}}'">
                <i data-lucide="settings" class="w-4 h-4 mr-1"></i>
                站长后台
            </button>
            {{#if Permissions.IsSuperAdmin}}
            <button class="btn btn-outline flex items-center" onclick="window.location.href='{{Links.SuperAdminUrl}}'">
                <i data-lucide="shield" class="w-4 h-4 mr-1"></i>
                超管后台
            </button>
            {{/if}}
        </div>
    </div>
</div>
{{/if}}

<!-- 退出登录按钮 -->
<div class="mx-4 my-6 logout-section">
    <button class="btn btn-destructive w-full py-3 flex items-center justify-center logout-btn">
        <i data-lucide="log-out" class="w-4 h-4 mr-2"></i>
        安全退出
    </button>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 1. 网站规则弹窗功能
        setupRuleModals();
        
        // 2. 退出确认弹窗功能
        setupLogoutModal();
        
        // 3. 生成规则表格
        generateLevelTable();
        generateTimeTable();
        
        // 4. 处理昵称颜色显示
        setupNicknameColor();
        
        // 5. 进度条动画
        setTimeout(() => {
            const progressFill = document.querySelector('.h-full.bg-gradient-to-r');
            
            if (progressFill) {
                const percentage = calculateExpProgress();
                progressFill.style.width = percentage + '%';
            }
        }, 500);

        // 6. 设置编辑资料下拉菜单
        setupEditProfileDropdown();
    });
    
    // 设置规则弹窗
    function setupRuleModals() {
        // 替换网站规则按钮点击事件，使用模态框而非跳转
        const moneyRuleBtn = document.querySelector('.grid-3 button:nth-child(1)');
        const levelRuleBtn = document.querySelector('.grid-3 button:nth-child(2)');
        const timeRuleBtn = document.querySelector('.grid-3 button:nth-child(3)');
        
        if (moneyRuleBtn) {
            const yaojingRuleModal = document.getElementById('yaojing-rule-modal');
            moneyRuleBtn.onclick = function(e) {
                e.preventDefault();
                yaojingRuleModal.style.display = 'flex';
                lucide.createIcons();
                return false;
            };
        }
        
        if (levelRuleBtn) {
            const levelRuleModal = document.getElementById('level-rule-modal');
            levelRuleBtn.onclick = function(e) {
                e.preventDefault();
                levelRuleModal.style.display = 'flex';
                lucide.createIcons();
                return false;
            };
        }
        
        if (timeRuleBtn) {
            const timeRuleModal = document.getElementById('time-rule-modal');
            timeRuleBtn.onclick = function(e) {
                e.preventDefault();
                timeRuleModal.style.display = 'flex';
                lucide.createIcons();
                return false;
            };
        }
        
        // 关闭按钮
        const closeButtons = document.querySelectorAll('.modal-close');
        closeButtons.forEach(button => {
            button.addEventListener('click', function() {
                document.querySelectorAll('[id$="-modal"]').forEach(modal => {
                    modal.style.display = 'none';
                });
            });
        });
        
        // 点击弹窗外部关闭
        document.querySelectorAll('[id$="-modal"]').forEach(modal => {
            modal.addEventListener('click', function(e) {
                if (e.target === this) {
                    this.style.display = 'none';
                }
            });
        });
    }
    
    // 设置退出确认弹窗
    function setupLogoutModal() {
        const logoutBtn = document.querySelector('.btn-destructive');
        const logoutModal = document.getElementById('logout-modal');
        const cancelLogout = document.getElementById('cancel-logout');
        
        if (logoutBtn && logoutModal) {
            // 覆盖原有的退出按钮链接点击事件
            logoutBtn.onclick = function(e) {
                e.preventDefault();
                logoutModal.style.display = 'flex';
                return false;
            };
            
            // 取消退出
            if (cancelLogout) {
                cancelLogout.addEventListener('click', function() {
                    logoutModal.style.display = 'none';
                });
            }
        }
    }
    
    // 生成等级规则表格
    function generateLevelTable() {
        const levelData = [
            { level: '1级', title: '无名的过客', exp: '1' },
            { level: '2级', title: '流浪的疾风', exp: '1001' },
            { level: '3级', title: '路旁的落叶', exp: '2001' },
            { level: '4级', title: '水面的小草', exp: '5001' },
            { level: '5级', title: '呢喃的歌声', exp: '10001' },
            { level: '6级', title: '地上的月影', exp: '50001' },
            { level: '7级', title: '奔跑的春风', exp: '100001' },
            { level: '8级', title: '深海的明珠', exp: '200001' },
            { level: '9级', title: '苍瀚的风云', exp: '300001' },
            { level: '10级', title: '摇曳的星辰', exp: '500001' },
            { level: '11级', title: '无尽的雷鸣', exp: '800001' },
            { level: '12级', title: '飞舞的流光', exp: '1000001' }
        ];

        const tbody = document.querySelector('#level-table tbody');
        if (tbody) {
            tbody.innerHTML = levelData.map(item => 
                `<tr class="border-b border-border-light">
                    <td class="p-2 text-center">${item.level}</td>
                    <td class="p-2 text-left">${item.title}</td>
                    <td class="p-2 text-right">${item.exp}</td>
                </tr>`
            ).join('');
        }
    }

    // 生成在线时间表格
    function generateTimeTable() {
        const timeData = [
            { days: '1-1', level: 1 }, { days: '2-2', level: 2 }, { days: '3-3', level: 3 },
            { days: '4-4', level: 4 }, { days: '5-5', level: 5 }, { days: '6-6', level: 6 },
            { days: '7-7', level: 7 }, { days: '8-8', level: 8 }, { days: '9-9', level: 9 },
            { days: '10-10', level: 10 }, { days: '11-20', level: 11 }, { days: '21-30', level: 12 },
            { days: '31-40', level: 13 }, { days: '41-50', level: 14 }, { days: '51-60', level: 15 },
            { days: '61-70', level: 16 }, { days: '71-80', level: 17 }, { days: '81-90', level: 18 },
            { days: '91-100', level: 19 }, { days: '101-200', level: 20 }, { days: '201-365', level: 21 },
            { days: '366+', level: 22 }
        ];

        const tbody = document.querySelector('#time-table tbody');
        if (tbody) {
            tbody.innerHTML = timeData.map(item => 
                `<tr class="border-b border-border-light">
                    <td class="p-2 text-center">${item.days}</td>
                    <td class="p-2 text-center"><img src="/bbs/medal/t${item.level}.gif" alt="在线${item.level}级" class="mx-auto"></td>
                </tr>`
            ).join('');
        }
    }

    // 处理昵称颜色显示
    function setupNicknameColor() {
        // 身份颜色映射 - 包含文字颜色和背景色
        var colorMap = {
            '绿色昵称': { color: '#25a444', bgColor: '#f0f9f4' },
            '红色昵称': { color: '#FF0000', bgColor: '#fef2f2' },
            '蓝色昵称': { color: '#228aff', bgColor: '#eff6ff' },
            '紫色昵称': { color: '#c000ff', bgColor: '#faf5ff' },
            '粉色昵称': { color: '#ff6363', bgColor: '#fef7f7' },
            '粉紫昵称': { color: '#ff00c0', bgColor: '#fdf2f8' },
            '红名VIP': { color: '#FF0000', bgColor: '#fef2f2' },
            '年费VIP': { color: '#c000ff', bgColor: '#faf5ff' },
            '靓': { color: '#FF7F00', bgColor: '#fff7ed' },
            '帅': { color: '#228aff', bgColor: '#eff6ff' },
            '金名VIP': { color: '#fa6700', bgColor: '#fff7ed' }
        };
        
        // 查找身份标签中的文本
        const identityTags = document.querySelectorAll('.bg-bg-vip.text-danger.border-danger.border-opacity-20');
        identityTags.forEach(tag => {
            const text = tag.textContent.trim();
            for (const [identity, colors] of Object.entries(colorMap)) {
                if (text.includes(identity)) {
                    tag.style.color = colors.color;
                    tag.style.backgroundColor = colors.bgColor;
                    tag.style.border = `1px solid ${colors.color}20`; // 20% 透明度的边框
                    break;
                }
            }
        });
        
        // 查找所有font-medium text-primary font-semibold text-right中的span元素，处理身份显示
        const infoValues = document.querySelectorAll('.font-medium.text-text-primary.text-right span');
        infoValues.forEach(span => {
            const text = span.textContent.trim();
            for (const [identity, colors] of Object.entries(colorMap)) {
                if (text.includes(identity)) {
                    span.style.color = colors.color;
                    // 如果span没有bg-bg-vip text-danger border-danger border-opacity-20类，给它添加合适的样式
                    if (!span.classList.contains('bg-bg-vip')) {
                        span.style.backgroundColor = colors.bgColor;
                        span.style.padding = '2px 6px';
                        span.style.borderRadius = '4px';
                        span.style.border = `1px solid ${colors.color}20`;
                        span.style.fontSize = '12px';
                        span.style.fontWeight = '500';
                        span.style.display = 'inline-block';
                    }
                    break;
                }
            }
        });
    }

    // 计算经验进度
    function calculateExpProgress() {
        try {
            // 获取用户当前经验值
            const userExp = {{UserInfo.Experience}};
            
            // 等级经验范围定义
            const levelRanges = [
                { level: 1, min: 1, max: 1000 },
                { level: 2, min: 1001, max: 2000 },
                { level: 3, min: 2001, max: 5000 },
                { level: 4, min: 5001, max: 10000 },
                { level: 5, min: 10001, max: 50000 },
                { level: 6, min: 50001, max: 100000 },
                { level: 7, min: 100001, max: 200000 },
                { level: 8, min: 200001, max: 300000 },
                { level: 9, min: 300001, max: 500000 },
                { level: 10, min: 500001, max: 800000 },
                { level: 11, min: 800001, max: 1000000 },
                { level: 12, min: 1000001, max: Infinity } // 最高级
            ];
            
            // 如果经验值为0或负数，返回0%
            if (userExp <= 0) {
                return 0;
            }
            
            // 查找当前等级范围
            let currentLevelRange = null;
            for (let i = 0; i < levelRanges.length; i++) {
                if (userExp >= levelRanges[i].min && userExp <= levelRanges[i].max) {
                    currentLevelRange = levelRanges[i];
                    break;
                }
            }
            
            // 如果找不到对应等级（理论上不应该发生），返回100%
            if (!currentLevelRange) {
                return 100;
            }
            
            // 如果是最高级（12级），直接返回100%
            if (currentLevelRange.level === 12) {
                return 100;
            }
            
            // 计算在当前等级范围内的进度
            const rangeMin = currentLevelRange.min;
            const rangeMax = currentLevelRange.max;
            const progressInRange = ((userExp - rangeMin) / (rangeMax - rangeMin)) * 100;
            
            // 确保进度在0-100之间
            return Math.min(Math.max(progressInRange, 0), 100);
            
        } catch (error) {
            console.log('计算经验进度时出错:', error);
            // 出错时返回默认值
            return 50;
        }
    }

    // 设置编辑资料下拉菜单
    function setupEditProfileDropdown() {
        const dropdownToggle = document.querySelector('#profile-edit-dropdown');
        const dropdownMenu = document.getElementById('profile-edit-dropdown-menu');

        if (!dropdownToggle || !dropdownMenu) {
            return; // 元素不存在则退出
        }

        // 点击按钮切换下拉菜单显示/隐藏
        dropdownToggle.addEventListener('click', function(event) {
            event.stopPropagation(); // 阻止事件冒泡，避免立即触发document的点击事件
            
            // 检查当前下拉菜单是否已经展开
            const isCurrentlyOpen = dropdownMenu.classList.contains('show');
            
            if (isCurrentlyOpen) {
                // 如果当前菜单已展开，直接关闭它
                dropdownMenu.classList.remove('show');
            } else {
                // 如果当前菜单未展开，先关闭其他所有下拉菜单，然后展开当前菜单
                closeAllDropdowns();
                dropdownMenu.classList.add('show');
            }
        });

        // 点击文档其他地方关闭下拉菜单
        document.addEventListener('click', function(event) {
            // 如果点击的不是下拉菜单内部，则关闭所有菜单
            if (!dropdownMenu.contains(event.target) && !dropdownToggle.contains(event.target)) {
                closeAllDropdowns();
            }
        });
    }

    // 关闭所有下拉菜单的通用函数
    function closeAllDropdowns() {
        // 关闭编辑资料下拉菜单
        const profileDropdown = document.getElementById('profile-edit-dropdown-menu');
        if (profileDropdown) {
            profileDropdown.classList.remove('show');
        }
        
        // 关闭右上角皮肤下拉菜单
        const skinDropdown = document.getElementById('skin-dropdown');
        if (skinDropdown) {
            skinDropdown.classList.remove('show');
        }
    }
</script>

<!-- 添加模态框功能 -->
<!-- 妖晶规则弹窗 -->
<div id="yaojing-rule-modal" class="fixed top-0 left-0 w-full h-full bg-black bg-opacity-50 z-modal items-center justify-center overflow-y-auto hidden">
    <div class="bg-white rounded-md p-5 w-[90%] max-w-[400px] shadow-lg mx-auto my-5">
        <div class="flex justify-between items-center mb-4">
            <div class="flex items-center">
                <i data-lucide="gem" class="w-6 h-6 text-primary mr-2.5"></i>
                <h3 class="text-lg font-medium">{{Assets.MoneyName}}获取规则</h3>
            </div>
            <button class="rule-modal-close modal-close bg-transparent border-none cursor-pointer text-text-secondary">
                <i data-lucide="x" class="w-5 h-5"></i>
            </button>
        </div>
        <div class="text-text-tertiary text-sm">
            <!-- 系统奖励卡片 -->
            <div class="mb-4 bg-bg-gray-50 rounded-md overflow-hidden shadow-sm">
                <div class="bg-primary-dark text-white py-2 px-3 font-medium flex items-center">
                    <i data-lucide="award" class="w-[18px] h-[18px] mr-2"></i>
                    <span>系统奖励</span>
                </div>
                <div class="p-3">
                    <table class="w-full border-collapse">
                        <tr class="border-b border-border-normal">
                            <td class="py-2 px-1 font-medium">回帖奖励</td>
                            <td class="py-2 px-1 text-right text-primary font-semibold">30 {{Assets.MoneyName}}</td>
                        </tr>
                        <tr class="border-b border-border-normal">
                            <td class="py-2 px-1 font-medium">发帖奖励</td>
                            <td class="py-2 px-1 text-right text-primary font-semibold">100 {{Assets.MoneyName}}</td>
                        </tr>
                        <tr>
                            <td class="py-2 px-1 font-medium">加精奖励</td>
                            <td class="py-2 px-1 text-right text-primary font-semibold">1000 {{Assets.MoneyName}}</td>
                        </tr>
                    </table>
                </div>
            </div>
            
            <!-- 论坛互动卡片 -->
            <div class="mb-4 bg-bg-gray-50 rounded-md overflow-hidden shadow-sm">
                <div class="bg-primary-dark text-white py-2 px-3 font-medium flex items-center">
                    <i data-lucide="message-square" class="w-[18px] h-[18px] mr-2"></i>
                    <span>论坛互动</span>
                </div>
                <div class="p-3">
                    <div class="grid grid-cols-3 gap-2">
                        <div class="bg-white rounded-md py-2 px-3 border border-border-normal flex items-center justify-center">
                            <i data-lucide="send" class="w-4 h-4 text-primary mr-1.5"></i>
                            <span>派币帖</span>
                        </div>
                        <div class="bg-white rounded-md py-2 px-3 border border-border-normal flex items-center justify-center">
                            <i data-lucide="help-circle" class="w-4 h-4 text-primary mr-1.5"></i>
                            <span>悬赏帖</span>
                        </div>
                        <div class="bg-white rounded-md py-2 px-3 border border-border-normal flex items-center justify-center">
                            <i data-lucide="gamepad-2" class="w-4 h-4 text-primary mr-1.5"></i>
                            <span>小游戏</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 充值兑换卡片 -->
            <div class="mb-4 bg-bg-gray-50 rounded-md overflow-hidden shadow-sm">
                <div class="bg-primary-dark text-white py-2 px-3 font-medium flex items-center">
                    <i data-lucide="credit-card" class="w-[18px] h-[18px] mr-2"></i>
                    <span>充值兑换</span>
                </div>
                <div class="p-3">
                    <div class="bg-white rounded-md p-3 border border-border-normal flex items-center">
                        <div class="w-9 h-9 bg-primary-alpha-10 rounded-full flex items-center justify-center mr-3">
                            <i data-lucide="gem" class="w-5 h-5 text-primary"></i>
                        </div>
                        <div class="flex-1">
                            <div class="font-medium text-text-primary">充值{{Assets.MoneyName}}</div>
                            <div class="text-xs text-text-secondary mt-0.5">站长直接增加或兑换</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 经验头衔等级规则弹窗 -->
<div id="level-rule-modal" class="fixed top-0 left-0 w-full h-full bg-black bg-opacity-50 z-modal items-center justify-center overflow-y-auto hidden">
    <div class="bg-white rounded-md p-5 w-[90%] max-w-[400px] shadow-lg mx-auto my-5">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-medium">经验获取&等级规则</h3>
            <button class="rule-modal-close modal-close bg-transparent border-none cursor-pointer text-text-secondary">
                <i data-lucide="x" class="w-5 h-5"></i>
            </button>
        </div>
        <div class="text-text-tertiary text-sm">
            <div class="mb-3">
                <table class="w-full border-collapse">
                    <tr class="border-b border-border-normal">
                        <td class="py-2 px-1 font-medium">回帖奖励</td>
                        <td class="py-2 px-1 text-right text-primary font-semibold">10 经验</td>
                    </tr>
                    <tr class="border-b border-border-normal">
                        <td class="py-2 px-1 font-medium">发帖奖励</td>
                        <td class="py-2 px-1 text-right text-primary font-semibold">50 经验</td>
                    </tr>
                    <tr>
                        <td class="py-2 px-1 font-medium">加精奖励</td>
                        <td class="py-2 px-1 text-right text-primary font-semibold">500 经验</td>
                    </tr>
                </table>
            </div>
            <div>
                <div class="max-h-[300px] overflow-y-auto overflow-hidden border border-border-normal rounded-md">
                    <table class="w-full border-collapse" id="level-table">
                        <thead>
                            <tr>
                                <th class="p-2 font-medium border-b border-border-normal bg-bg-gray-50 sticky top-0 text-center">等级</th>
                                <th class="p-2 font-medium border-b border-border-normal bg-bg-gray-50 sticky top-0 text-left">头衔</th>
                                <th class="p-2 font-medium border-b border-border-normal bg-bg-gray-50 sticky top-0 text-right">经验值</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- 表格内容将通过JavaScript生成 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 在线天数与等级图标规则弹窗 -->
<div id="time-rule-modal" class="fixed top-0 left-0 w-full h-full bg-black bg-opacity-50 z-modal items-center justify-center overflow-y-auto hidden">
    <div class="bg-white rounded-md p-5 w-[90%] max-w-[400px] shadow-lg mx-auto my-5">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-medium">在线天数&等级图标</h3>
            <button class="rule-modal-close modal-close bg-transparent border-none cursor-pointer text-text-secondary">
                <i data-lucide="x" class="w-5 h-5"></i>
            </button>
        </div>
        <div class="text-text-tertiary text-sm">
            <div class="max-h-[350px] overflow-y-auto overflow-hidden border border-border-normal rounded-md">
                <table class="w-full border-collapse" id="time-table">
                    <thead>
                        <tr>
                            <th class="p-2 font-medium border-b border-border-normal bg-bg-gray-50 sticky top-0 text-center">在线天数</th>
                            <th class="p-2 font-medium border-b border-border-normal bg-bg-gray-50 sticky top-0 text-center">等级图标</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- 表格内容将通过JavaScript生成 -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- 退出确认弹窗 -->
<div id="logout-modal" class="fixed top-0 left-0 w-full h-full bg-black bg-opacity-50 z-modal items-center justify-center overflow-y-auto hidden">
    <div class="bg-white rounded-md p-5 w-[80%] max-w-[320px] shadow-lg">
        <h3 class="mt-0 mb-4 text-lg text-text-primary">确认退出</h3>
        <p class="mb-5 text-text-tertiary text-sm">您确定要退出登录吗？</p>
        <div class="flex justify-end gap-2">
            <button id="cancel-logout" class="py-1.5 px-3 text-sm bg-transparent text-text-tertiary border border-border-normal rounded hover:bg-bg-primary hover:border-border-dark hover:text-text-primary transition-colors">取消</button>
            <button id="confirm-logout" class="py-1.5 px-3 text-sm bg-danger text-white border border-danger rounded hover:bg-danger-dark hover:border-danger-dark transition-colors" onclick="window.location.href='{{Links.LogoutUrl}}'">确认退出</button>
        </div>
    </div>
</div>